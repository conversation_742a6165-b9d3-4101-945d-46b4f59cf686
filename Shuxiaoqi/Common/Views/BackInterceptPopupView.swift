import UIKit
import SnapKit

/// 返回拦截弹窗，提供“不保存返回”和“存草稿”两个选项
class BackInterceptPopupView: UIView {
    enum ActionType {
        case discard   // 不保存返回
        case saveDraft // 存草稿
    }

    // MARK: - Public
    var onActionSelected: ((ActionType) -> Void)?

    // MARK: - Private
    private let container = UIView()
    private let popupWidth: CGFloat = 128
    private let popupHeight: CGFloat = 81 // 40 + 1 + 40

    // MARK: - Init
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    // MARK: - UI
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.3)

        // 容器
        container.backgroundColor = .white
        container.layer.cornerRadius = 8
        addSubview(container)
        container.snp.makeConstraints { make in
            make.width.equalTo(popupWidth)
            make.height.equalTo(popupHeight)
            make.center.equalToSuperview()
        }

        // 第一行：不保存返回
        let discardButton = createRow(title: "不保存返回", iconName: "icon_pop_back")
        container.addSubview(discardButton)
        discardButton.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(40)
        }
        // 设置“不保存返回”按钮文字颜色
        discardButton.setTitleColor(UIColor(hex: "#FF1515"), for: .normal)
        //字号和字体
        discardButton.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        discardButton.addTarget(self, action: #selector(discardTapped), for: .touchUpInside)

        // 分割线
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#BFBFBF")
        container.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.top.equalTo(discardButton.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(1)
        }

        // 第二行：存草稿
        let draftButton = createRow(title: "存草稿", iconName: "icon_pop_draft")
        draftButton.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        draftButton.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        container.addSubview(draftButton)
        draftButton.snp.makeConstraints { make in
            make.top.equalTo(separator.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(40)
        }
        draftButton.addTarget(self, action: #selector(draftTapped), for: .touchUpInside)

        // 点击背景关闭
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        addGestureRecognizer(tap)
    }

    private func createRow(title: String, iconName: String) -> UIButton {
        let button = UIButton(type: .custom)
        button.setTitle(title, for: .normal)
        button.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        button.contentHorizontalAlignment = .left
        button.backgroundColor = .clear
        if let icon = UIImage(named: iconName) {
            button.setImage(icon, for: .normal)
        }
        // 调整内边距
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 13, bottom: 0, right: 0)
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: 13 + 18 + 8 - 13 /*iconLeft+iconWidth+spacing - original left*/, bottom: 0, right: 0)
        return button
    }

    // MARK: - Public Present Method
    /// 将弹窗显示在 anchorView 右侧、其下方，避免超出屏幕
    /// - Parameters:
    ///   - anchorView: 锚点视图
    ///   - offsetX: X 方向偏移，默认 4pt
    ///   - offsetY: Y 方向偏移，默认 4pt
    func present(below anchorView: UIView, offsetX: CGFloat = 8, offsetY: CGFloat = 4) {
        guard let window = anchorView.window else { return }

        // 添加到 window，覆盖全屏点击区域
        window.addSubview(self)
        self.frame = window.bounds

        // 将 anchorView 的 frame 转换到当前坐标系
        let anchorFrame = anchorView.convert(anchorView.bounds, to: self)

        // 计算期望的左侧位置（anchor 左侧 + offsetX）
        var expectedLeft = anchorFrame.minX + offsetX
        // 若弹窗右侧超出屏幕，则向左收回
        let maxRightAllowed = window.bounds.width - 12 // 12pt 预留安全边距
        if expectedLeft + popupWidth > maxRightAllowed {
            expectedLeft = maxRightAllowed - popupWidth
        }
        // 若弹窗左侧超出屏幕，也调整
        if expectedLeft < 12 {
            expectedLeft = 12
        }

        // 设置 container 约束：左对齐 anchor 右侧，顶部在 anchor 底部 + offsetY
        container.snp.remakeConstraints { make in
            make.top.equalTo(anchorFrame.maxY + offsetY)
            make.left.equalTo(expectedLeft)
            make.width.equalTo(popupWidth)
            make.height.equalTo(popupHeight)
        }

        // 布局更新
        layoutIfNeeded()
    }

    // MARK: - Actions
    @objc private func discardTapped() {
        onActionSelected?(.discard)
        dismiss()
    }

    @objc private func draftTapped() {
        onActionSelected?(.saveDraft)
        dismiss()
    }

    @objc private func backgroundTapped() {
        dismiss()
    }

    private func dismiss() {
        removeFromSuperview()
    }
} 
